import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

export async function seedAssistantMessageTypes() {
  console.log('Seeding AssistantMessageTypes...');

  const messageTypes = [
    {
      name: 'System',
      description: 'System or instruction messages that provide context and guidelines to the AI assistant'
    },
    {
      name: 'User', 
      description: 'Messages sent by users as input to the AI assistant'
    },
    {
      name: 'Assistant',
      description: 'Response messages generated by the AI assistant'
    }
  ];

  for (const messageType of messageTypes) {
    await prisma.assistantMessageType.upsert({
      where: { name: messageType.name },
      update: {
        description: messageType.description
      },
      create: {
        name: messageType.name,
        description: messageType.description
      }
    });
    console.log(`✓ Created/Updated AssistantMessageType: ${messageType.name}`);
  }

  console.log('AssistantMessageTypes seeding completed.');
}

// Run this script directly if called
if (require.main === module) {
  seedAssistantMessageTypes()
    .catch((e) => {
      console.error('Error seeding AssistantMessageTypes:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
